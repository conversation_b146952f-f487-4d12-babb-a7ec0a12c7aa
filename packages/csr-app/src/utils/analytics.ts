/**
 * CSR应用统一事件上报配置
 * 集成新的shared analytics架构
 */

import { 
  AnalyticsManager, 
  ByteDanceProvider, 
  GoogleAnalyticsProvider,
  type EventData,
  ReportEvent
} from '@magic-partner/shared/analytics'

// 创建全局分析管理器
const analytics = new AnalyticsManager({
  debug: import.meta.env.DEV
})

// 主要使用字节跳动火山引擎
analytics.addProvider(new ByteDanceProvider())

// 可选：添加谷歌分析作为备用
if (import.meta.env.VITE_ENABLE_GA === 'true') {
  analytics.addProvider(new GoogleAnalyticsProvider('G-BCD38QPPKH'))
}

/**
 * 统一事件上报函数 - 替代原有的 reportEvent
 * 保持向后兼容
 */
export function reportEvent(eventType: ReportEvent, eventData: EventData = {}): boolean {
  try {
    // 添加CSR应用特有的环境信息
    const enrichedData = {
      ...eventData,
      eventTime: Date.now(),
      app_host: location.host,
      app_env: getMobileOrPCEnv(),
      app_version: getAppVersion(),
      os: getOSInfo(),
      ua: getUserAgent(),
      userId: getUserId(),
      route: window.location.pathname
    }

    analytics.track(eventType, enrichedData)
    return true
  } catch (error) {
    console.error('[Analytics] 事件上报失败:', error)
    return false
  }
}

/**
 * 页面浏览上报
 */
export function reportPageView(path?: string) {
  analytics.pageview(path || window.location.pathname)
}

/**
 * 用户识别
 */
export function reportUserIdentify(userId: string) {
  analytics.identify(userId)
}

// 保持向后兼容，重新导出事件枚举
export { ReportEvent } from '@magic-partner/shared/analytics'

// 工具函数 - 复用现有逻辑
function getMobileOrPCEnv(): string {
  // 复用现有的环境检测逻辑
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  const isApp = window.location.hostname.includes('app') // 简化检测
  
  if (isApp) return 'app'
  return isMobile ? 'mobile' : 'pc'
}

function getAppVersion(): string {
  // 从环境变量或package.json获取版本
  return import.meta.env.VITE_APP_VERSION || '1.0.0'
}

function getOSInfo(): object {
  // 简化版OS信息
  return {
    name: navigator.platform,
    version: navigator.appVersion
  }
}

function getUserAgent(): string {
  return navigator.userAgent
}

function getUserId(): string {
  try {
    const user = localStorage.getItem('user')
    if (user) {
      const userData = JSON.parse(user)
      return userData.uuid || userData.id || ''
    }
  } catch (error) {
    console.warn('获取用户ID失败:', error)
  }
  return ''
}

// 导出管理器实例，供其他模块使用
export { analytics }