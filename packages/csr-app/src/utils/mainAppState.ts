/**
 * 主应用状态管理
 * 用于在微前端环境中管理主应用传递的状态
 * 避免循环依赖问题
 */

// 主应用页面状态类型定义
export interface MainAppPageState {
  currentUrl?: string
  page?: any
  [key: string]: any
}

// 全局变量存储主应用的页面状态
let mainAppPageState: MainAppPageState | null = null

/**
 * 获取主应用页面状态
 * @returns 主应用页面状态或null
 */
export function getMainAppPageState(): MainAppPageState | null {
  return mainAppPageState
}

/**
 * 设置主应用页面状态
 * @param state 新的页面状态
 */
export function setMainAppPageState(state: MainAppPageState | null): void {
  mainAppPageState = state
  console.log('📥 CSR应用: 保存主应用页面状态', mainAppPageState)
}

/**
 * 更新主应用页面状态（部分更新）
 * @param updates 要更新的状态字段
 */
export function updateMainAppPageState(updates: Partial<MainAppPageState>): void {
  if (mainAppPageState) {
    mainAppPageState = { ...mainAppPageState, ...updates }
  } else {
    mainAppPageState = updates
  }
  console.log('📥 CSR应用: 更新主应用页面状态', mainAppPageState)
}

/**
 * 清除主应用页面状态
 */
export function clearMainAppPageState(): void {
  mainAppPageState = null
  console.log('🗑️ CSR应用: 清除主应用页面状态')
}

/**
 * 检查是否有主应用页面状态
 * @returns 是否有状态
 */
export function hasMainAppPageState(): boolean {
  return mainAppPageState !== null
}
