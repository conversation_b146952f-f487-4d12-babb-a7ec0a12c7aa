/**
 * 统一事件上报类型定义
 */

// 重新导出现有的事件枚举，保持兼容性
export { ReportEvent } from '../../../csr-app/src/interface/report'

// 事件数据接口
export interface EventData {
  [key: string]: any
}

// 统一的事件上报接口
export interface AnalyticsProvider {
  /** 提供者名称 */
  name: string
  
  /** 上报事件 */
  track(eventType: string, eventData?: EventData): boolean | void
  
  /** 页面浏览事件 */
  pageview?(path: string): void
  
  /** 用户识别 */
  identify?(userId: string): void
}

// 配置接口
export interface AnalyticsConfig {
  /** 是否启用调试模式 */
  debug?: boolean
  
  /** 字节跳动配置 */
  bytedance?: {
    appId?: string
    enabled?: boolean
  }
  
  /** 谷歌分析配置 */
  google?: {
    measurementId?: string
    enabled?: boolean
  }
}