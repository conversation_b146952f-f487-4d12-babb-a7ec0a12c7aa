/**
 * 统一事件上报工具函数
 */

import type { EventData } from './types'
import { ReportEvent } from './types'

/**
 * 创建标准事件数据
 */
export function createEventData(baseData: EventData = {}): EventData {
  return {
    ...baseData,
    eventTime: Date.now(),
    app_host: typeof window !== 'undefined' ? location.host : '',
    app_version: process.env.VERSION || '1.0.0',
    route: typeof window !== 'undefined' ? window.location.pathname : '',
    timestamp: Date.now()
  }
}

/**
 * 创建用户相关事件数据
 */
export function createUserEventData(userId?: string, additionalData: EventData = {}): EventData {
  return createEventData({
    ...additionalData,
    userId: userId || getUserId()
  })
}

/**
 * 创建页面浏览事件数据
 */
export function createPageViewData(
  pagePath?: string, 
  pageTitle?: string, 
  additionalData: EventData = {}
): EventData {
  return createEventData({
    ...additionalData,
    page_path: pagePath || (typeof window !== 'undefined' ? window.location.pathname : ''),
    page_title: pageTitle || (typeof document !== 'undefined' ? document.title : ''),
    page_referrer: typeof document !== 'undefined' ? document.referrer : ''
  })
}

/**
 * 创建故事相关事件数据
 */
export function createStoryEventData(
  storyId: string,
  storyTitle?: string,
  characterId?: string,
  additionalData: EventData = {}
): EventData {
  return createEventData({
    ...additionalData,
    story_id: storyId,
    story_title: storyTitle,
    character_id: characterId
  })
}

/**
 * 创建支付相关事件数据
 */
export function createPaymentEventData(
  amount?: number,
  currency?: string,
  paymentMethod?: string,
  productId?: string,
  additionalData: EventData = {}
): EventData {
  return createEventData({
    ...additionalData,
    value: amount,
    currency: currency || 'USD',
    payment_method: paymentMethod,
    product_id: productId
  })
}

/**
 * 创建错误事件数据
 */
export function createErrorEventData(
  errorMessage: string,
  errorCode?: string,
  errorStack?: string,
  additionalData: EventData = {}
): EventData {
  return createEventData({
    ...additionalData,
    error_message: errorMessage,
    error_code: errorCode,
    error_stack: errorStack
  })
}

/**
 * 获取用户ID (从localStorage)
 */
function getUserId(): string {
  if (typeof window === 'undefined') return ''
  
  try {
    const user = localStorage.getItem('user')
    if (user) {
      const userData = JSON.parse(user)
      return userData.uuid || userData.id || ''
    }
  } catch (error) {
    console.warn('获取用户ID失败:', error)
  }
  
  return ''
}

/**
 * 常用事件快捷上报函数
 */
export const EventHelpers = {
  /**
   * 用户登录事件
   */
  userLogin: (userId: string, loginMethod?: string) => ({
    event: ReportEvent.LoginSuccess,
    data: createUserEventData(userId, { login_method: loginMethod })
  }),

  /**
   * 页面浏览事件
   */
  pageView: (pagePath?: string, pageTitle?: string) => ({
    event: ReportEvent.PageView,
    data: createPageViewData(pagePath, pageTitle)
  }),

  /**
   * 故事点击事件
   */
  storyClick: (storyId: string, storyTitle?: string) => ({
    event: ReportEvent.ClickIndexPageStoryCard,
    data: createStoryEventData(storyId, storyTitle)
  }),

  /**
   * 游戏开始事件
   */
  gameStart: (storyId: string, characterId?: string) => ({
    event: ReportEvent.StartChat,
    data: createStoryEventData(storyId, undefined, characterId)
  }),

  /**
   * 支付成功事件
   */
  paymentSuccess: (amount: number, currency?: string, productId?: string) => ({
    event: ReportEvent.PaymentSuccess,
    data: createPaymentEventData(amount, currency, undefined, productId)
  }),

  /**
   * API错误事件
   */
  apiError: (errorMessage: string, errorCode?: string) => ({
    event: ReportEvent.ApiError,
    data: createErrorEventData(errorMessage, errorCode)
  })
}