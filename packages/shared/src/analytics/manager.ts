/**
 * 统一事件上报管理器
 * 简化版本，专注于调用第三方库方法
 */

import type { AnalyticsProvider, EventData, AnalyticsConfig } from './types'

export class AnalyticsManager {
  private providers: AnalyticsProvider[] = []
  private config: AnalyticsConfig = {}

  constructor(config?: AnalyticsConfig) {
    this.config = config || {}
  }

  /**
   * 添加上报提供者
   */
  addProvider(provider: AnalyticsProvider) {
    this.providers.push(provider)
    if (this.config.debug) {
      console.log(`[Analytics] 添加提供者: ${provider.name}`)
    }
  }

  /**
   * 移除上报提供者
   */
  removeProvider(providerName: string) {
    this.providers = this.providers.filter(p => p.name !== providerName)
    if (this.config.debug) {
      console.log(`[Analytics] 移除提供者: ${providerName}`)
    }
  }

  /**
   * 统一事件上报
   */
  track(eventType: string, eventData?: EventData) {
    if (this.config.debug) {
      console.log(`[Analytics] 上报事件: ${eventType}`, eventData)
    }

    this.providers.forEach(provider => {
      try {
        provider.track(eventType, eventData)
      } catch (error) {
        console.error(`[Analytics] ${provider.name} 上报失败:`, error)
      }
    })
  }

  /**
   * 页面浏览事件
   */
  pageview(path: string) {
    if (this.config.debug) {
      console.log(`[Analytics] 页面浏览: ${path}`)
    }

    this.providers.forEach(provider => {
      try {
        if (provider.pageview) {
          provider.pageview(path)
        }
      } catch (error) {
        console.error(`[Analytics] ${provider.name} 页面浏览上报失败:`, error)
      }
    })
  }

  /**
   * 用户识别
   */
  identify(userId: string) {
    if (this.config.debug) {
      console.log(`[Analytics] 用户识别: ${userId}`)
    }

    this.providers.forEach(provider => {
      try {
        if (provider.identify) {
          provider.identify(userId)
        }
      } catch (error) {
        console.error(`[Analytics] ${provider.name} 用户识别失败:`, error)
      }
    })
  }

  /**
   * 获取已注册的提供者列表
   */
  getProviders(): string[] {
    return this.providers.map(p => p.name)
  }
}