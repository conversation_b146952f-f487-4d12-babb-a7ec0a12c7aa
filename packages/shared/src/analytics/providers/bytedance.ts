/**
 * 字节跳动火山引擎上报提供者
 * 直接调用现有的 window.collectEvent 方法
 */

import type { AnalyticsProvider, EventData } from '../types'

declare global {
  interface Window {
    collectEvent?: (eventType: string, eventData?: any) => void
  }
}

export class ByteDanceProvider implements AnalyticsProvider {
  name = 'bytedance'

  /**
   * 上报事件 - 直接调用 window.collectEvent
   */
  track(eventType: string, eventData?: EventData): boolean {
    if (!window.collectEvent) {
      console.warn('[ByteDance] window.collectEvent 不存在')
      return false
    }

    try {
      window.collectEvent(eventType, eventData)
      return true
    } catch (error) {
      console.error('[ByteDance] 事件上报失败:', error)
      return false
    }
  }

  /**
   * 页面浏览事件
   */
  pageview(path: string) {
    this.track('PageView', {
      page_path: path,
      page_title: document.title
    })
  }

  /**
   * 用户识别
   */
  identify(userId: string) {
    this.track('UserIdentify', {
      user_id: userId
    })
  }
}