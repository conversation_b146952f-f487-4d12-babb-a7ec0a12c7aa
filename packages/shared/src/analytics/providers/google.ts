/**
 * 谷歌分析上报提供者
 * 直接调用现有的 window.gtag 方法
 */

import type { AnalyticsProvider, EventData } from '../types'

declare global {
  interface Window {
    gtag?: (...args: any[]) => void
  }
}

export class GoogleAnalyticsProvider implements AnalyticsProvider {
  name = 'google'
  private measurementId?: string

  constructor(measurementId?: string) {
    this.measurementId = measurementId
  }

  /**
   * 上报事件 - 直接调用 window.gtag
   */
  track(eventType: string, eventData?: EventData): boolean {
    if (!window.gtag) {
      console.warn('[Google Analytics] window.gtag 不存在')
      return false
    }

    try {
      window.gtag('event', eventType, eventData)
      return true
    } catch (error) {
      console.error('[Google Analytics] 事件上报失败:', error)
      return false
    }
  }

  /**
   * 页面浏览事件
   */
  pageview(path: string) {
    if (!window.gtag) {
      console.warn('[Google Analytics] window.gtag 不存在')
      return
    }

    try {
      if (this.measurementId) {
        window.gtag('config', this.measurementId, {
          page_path: path,
          page_title: document.title
        })
      } else {
        window.gtag('event', 'page_view', {
          page_path: path,
          page_title: document.title
        })
      }
    } catch (error) {
      console.error('[Google Analytics] 页面浏览上报失败:', error)
    }
  }

  /**
   * 用户识别
   */
  identify(userId: string) {
    if (!window.gtag) {
      console.warn('[Google Analytics] window.gtag 不存在')
      return
    }

    try {
      window.gtag('config', this.measurementId || 'GA_MEASUREMENT_ID', {
        user_id: userId
      })
    } catch (error) {
      console.error('[Google Analytics] 用户识别失败:', error)
    }
  }
}