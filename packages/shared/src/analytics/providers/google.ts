/**
 * 谷歌分析上报提供者
 * 优先使用 nuxt-gtag，回退到 window.gtag
 */

import type { AnalyticsProvider, EventData } from '../types'

declare global {
  interface Window {
    gtag?: (...args: any[]) => void
    $gtag?: (...args: any[]) => void
  }
}

export class GoogleAnalyticsProvider implements AnalyticsProvider {
  name = 'google'
  private measurementId?: string

  constructor(measurementId?: string) {
    this.measurementId = measurementId
  }

  /**
   * 获取可用的gtag实例
   */
  private getGtag() {
    // 优先使用 nuxt-gtag 提供的 $gtag
    if (typeof window !== 'undefined' && window.$gtag) {
      return window.$gtag
    }
    // 回退到原生 gtag
    if (typeof window !== 'undefined' && window.gtag) {
      return window.gtag
    }
    return null
  }

  /**
   * 上报事件 - 优先使用 nuxt-gtag
   */
  track(eventType: string, eventData?: EventData): boolean {
    const gtag = this.getGtag()

    if (!gtag) {
      console.warn('[Google Analytics] gtag 不可用')
      return false
    }

    try {
      gtag('event', eventType, eventData)
      return true
    } catch (error) {
      console.error('[Google Analytics] 事件上报失败:', error)
      return false
    }
  }

  /**
   * 页面浏览事件
   */
  pageview(path: string) {
    if (!window.gtag) {
      console.warn('[Google Analytics] window.gtag 不存在')
      return
    }

    try {
      if (this.measurementId) {
        window.gtag('config', this.measurementId, {
          page_path: path,
          page_title: document.title,
        })
      } else {
        window.gtag('event', 'page_view', {
          page_path: path,
          page_title: document.title,
        })
      }
    } catch (error) {
      console.error('[Google Analytics] 页面浏览上报失败:', error)
    }
  }

  /**
   * 用户识别
   */
  identify(userId: string) {
    if (!window.gtag) {
      console.warn('[Google Analytics] window.gtag 不存在')
      return
    }

    try {
      window.gtag('config', this.measurementId || 'GA_MEASUREMENT_ID', {
        user_id: userId,
      })
    } catch (error) {
      console.error('[Google Analytics] 用户识别失败:', error)
    }
  }
}
