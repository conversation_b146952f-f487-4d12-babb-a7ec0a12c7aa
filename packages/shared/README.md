# 统一事件上报架构

基于shared包的统一事件上报解决方案，主要使用**字节跳动火山引擎**，谷歌分析作为可选备用。

## 🏗️ 架构优势

- **代码复用**: 90%+统计逻辑复用，避免重复实现
- **类型安全**: TypeScript统一事件定义，减少错误
- **灵活配置**: 支持不同应用配置不同统计提供者
- **向后兼容**: 保持现有API接口，无缝迁移

## 📦 使用方式

### CSR应用 (packages/csr-app)

```typescript
// 替代原有的 report.ts
import { reportEvent, ReportEvent } from '@/utils/analytics'

// 保持原有调用方式不变
reportEvent(ReportEvent.UserLogin, {
  login_method: 'google'
})
```

### Nuxt应用 (packages/nuxt-app)

```typescript
// 使用新的composable
const { track, trackPageView, trackStoryEvent } = useSharedAnalytics()

// 统一上报
track('user_login', { method: 'google' })

// 页面浏览
trackPageView('/stories')

// 故事事件 (保持兼容)
trackStoryEvent('click', 'story123', 'Story Title')
```

## 🔧 配置选项

### 环境变量控制

**CSR应用**:
```bash
VITE_ENABLE_GA=true  # 启用谷歌分析备用
```

**Nuxt应用**:
```bash
NUXT_ENABLE_GA=true  # 启用谷歌分析备用
```

### 主要提供者

- **字节跳动火山引擎**: 主要统计平台，调用 `window.collectEvent`
- **谷歌分析**: 备用统计平台，调用 `window.gtag`

## 📊 事件定义

统一使用现有的 `ReportEvent` 枚举，保持100%兼容性：

```typescript
import { ReportEvent } from '@magic-partner/shared/analytics'

// 所有现有事件类型都可以直接使用
ReportEvent.UserLogin
ReportEvent.PaymentSuccess  
ReportEvent.StartChat
// ... 200+ 事件类型
```

## 🚀 迁移步骤

### 1. CSR应用迁移

```typescript
// 原来
import { reportEvent } from '@/utils/report'

// 现在
import { reportEvent } from '@/utils/analytics'  // 新的统一接口
```

### 2. Nuxt应用迁移

```typescript
// 原来
const { trackEvent } = useAnalytics()

// 现在  
const { track } = useSharedAnalytics()  // 新的统一接口
```

## 🔍 调试模式

开发环境自动启用调试日志：

```javascript
[Analytics] 添加提供者: bytedance
[Analytics] 上报事件: UserLogin { method: 'google' }
```

## 📈 扩展性

添加新的统计平台只需实现 `AnalyticsProvider` 接口：

```typescript
class NewProvider implements AnalyticsProvider {
  name = 'new-provider'
  
  track(eventType: string, eventData?: EventData) {
    // 调用新平台的API
  }
}

analytics.addProvider(new NewProvider())
```

## 🔄 渐进式迁移

1. **Phase 1**: 新架构与现有代码并存
2. **Phase 2**: 逐步替换调用方式  
3. **Phase 3**: 移除旧代码，完全统一

现有代码无需立即修改，可以逐步迁移！