/**
 * 火山引擎统计分析客户端插件
 * 初始化火山引擎统计
 */

export default defineNuxtPlugin(() => {
  // 只在客户端运行
  if (!process.client) return

  const config = useRuntimeConfig()
  
  // 火山引擎配置
  const volcConfig = {
    // 从环境变量获取APP ID，开发环境使用测试ID
    app_id: process.env.NODE_ENV === 'development' ? 20005873 : (config.public.volcAppId || 20005873),
    channel: 'web',
    log: process.env.NODE_ENV === 'development', // 开发环境启用日志
    disable_sdk_monitor: false,
    disable_auto_pv: true, // 禁用自动页面浏览统计，手动控制
  }

  // 等待火山引擎脚本加载完成
  const initVolcAnalytics = () => {
    if (typeof window !== 'undefined' && (window as any).collectEvent) {
      console.log('🌋 火山引擎已加载')
      
      // 初始化火山引擎
      try {
        ;(window as any).collectEvent('config', volcConfig)
        console.log('🌋 火山引擎初始化成功', volcConfig)
      } catch (error) {
        console.error('🌋 火山引擎初始化失败:', error)
      }
    } else {
      // 如果脚本还没加载，等待一段时间后重试
      setTimeout(initVolcAnalytics, 100)
    }
  }

  // 页面加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initVolcAnalytics)
  } else {
    initVolcAnalytics()
  }

  // 开发环境调试信息
  if (process.env.NODE_ENV === 'development') {
    setTimeout(() => {
      console.log('🔧 火山引擎调试信息:')
      console.log('- APP ID:', volcConfig.app_id)
      console.log('- collectEvent 可用:', !!(window as any).collectEvent)
      console.log('- 配置:', volcConfig)
    }, 2000)
  }
})

// 类型声明
declare global {
  interface Window {
    collectEvent?: (eventType: string, eventData?: any) => void
  }
}
