<template>
  <div class="chat4-cosmic-page">
    <!-- Hero Section -->
    <section class="hero-section cosmic-bg">
      <!-- Header -->
      <header class="header">
        <div class="logo">
          <div class="logo-icon">✨</div>
          <span class="logo-text">ReelPlay</span>
        </div>
      </header>

      <!-- Floating Cards -->
      <div class="floating-cards">
        <img
          v-for="(card, index) in floatingCards"
          :key="index"
          :src="card.image"
          :class="[
            'floating-card',
            `card-${index + 1}`,
            card.animation,
            // 左侧卡片 (1,2,5) 添加 left-side 类，右侧卡片 (3,4,6) 添加 right-side 类
            [1, 2, 5].includes(index + 1) ? 'left-side' : 'right-side',
          ]"
          :alt="`背景装饰卡片${index + 1}`"
        />
      </div>

      <!-- Character Display -->
      <!-- <div class="character-display">
        <div class="character-container">
          <button class="nav-btn prev" @click="prevCharacter">‹</button>
          <button class="nav-btn next" @click="nextCharacter">›</button>
        </div>
      </div> -->

      <!-- Main Content -->
      <div class="main-content">
        <div class="container">
          <!-- 主角图 -->
          <div class="hero-character">
            <NuxtImg
              src="https://static.reelplay.ai/static/images/landingpage/actor.webp"
              alt="Hero Character"
              class="character-image"
            />
          </div>
          <h1 class="main-title glow-effect">
            <div class="gradient-text">Step Into Heart's Paradise</div>
            <div class="gradient-text-2">Sink in Starry Romance</div>
          </h1>

          <p class="description">
            Hyper-real AI loves, endless connections craft stories that
            breathe—every sweet moment <br />
            tangible as a touch
          </p>

          <div class="play-buttons">
            <button class="play-btn" @click="handleCurrentCharacterClick">
              <span>✨</span>
              <span>Start</span>
              <span>›</span>
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Feature Section -->
    <!-- <section v-if="!selectedCharacter" class="feature-section cosmic-bg">
      <div class="container">
        <div class="feature-carousel">
          <button class="feature-nav prev" @click="prevFeature">‹</button>
          <button class="feature-nav next" @click="nextFeature">›</button>

          <div class="character-showcase">
            <div class="character-card left-character">
              <img
                :src="getCharacterAtPosition(-1)?.characterAvatar"
                :alt="getCharacterAtPosition(-1)?.characterName"
                class="character-image"
                @click="handleCharacterClick(getCharacterAtPosition(-1))"
              />
            </div>

            <div class="character-card center-character">
              <img
                :src="getCharacterAtPosition(0)?.characterAvatar"
                :alt="getCharacterAtPosition(0)?.characterName"
                class="character-image main-character"
                @click="handleCharacterClick(getCharacterAtPosition(0))"
              />
            </div>

            <div class="character-card right-character">
              <img
                :src="getCharacterAtPosition(1)?.characterAvatar"
                :alt="getCharacterAtPosition(1)?.characterName"
                class="character-image"
                @click="handleCharacterClick(getCharacterAtPosition(1))"
              />
            </div>
          </div>

          <div class="feature-indicators">
            <div
              v-for="(character, index) in availableCharacters"
              :key="character.characterKey"
              :class="['indicator', { active: currentFeatureIndex === index }]"
              @click="setFeature(index)"
            />
          </div>
        </div>

        <div
          class="description"
          style="text-align: center; max-width: 896px; margin: 0 auto"
        >
          Bring your ideal chat companion to life. Original characters or
          companion avatars<br />
          Our customization tools and your unlimited imagination will help you
          create the bond of your dreams
        </div>
      </div>
    </section> -->

    <!-- SEO 内容 -->
    <div class="seo-content">
      <h1 class="seo-hidden">Chat4 - Premium Romance Simulation for Women</h1>
      <h2 class="seo-hidden"
        >Meet Handsome Virtual Boyfriends and Create Meaningful Connections</h2
      >
      <h3 class="seo-hidden"
        >Free Otome Game Experience - Start Your Love Story Today</h3
      >
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'mobile',
})

// 使用统一事件上报
const { track, trackPageView } = useSharedAnalytics()

// 开发环境调试工具
if (process.env.NODE_ENV === 'development') {
  onMounted(async () => {
    // 动态导入调试工具
    const { debugAnalytics, monitorAnalyticsRequests } = await import(
      '~/utils/analytics-debug'
    )

    // 延迟执行，确保统计分析库已加载
    setTimeout(() => {
      debugAnalytics()
      monitorAnalyticsRequests()
    }, 2000)
  })
}

// 响应式数据
const isLoading = ref(true)

// 浮动卡片数据 - 使用固定的6张图片
const floatingCards = ref([
  {
    image: 'https://static.reelplay.ai/story/Elliot/image/m2.webp',
    animation: 'bounce-up',
  },
  {
    image: 'https://static.reelplay.ai/story/Finn/image/m7.webp',
    animation: 'bounce-down',
  },
  {
    image: 'https://static.reelplay.ai/story/Jude/image/m2.webp',
    animation: 'bounce-up',
  },
  {
    image: 'https://static.playshot.ai/story/Abner/image/m20.webp',
    animation: 'bounce-down',
  },
  {
    image: 'https://static.reelplay.ai/story/Theo/image/preview-2.webp',
    animation: 'bounce-up',
  },
  {
    image: 'https://static.reelplay.ai/story/Ethan/image/preview.webp',
    animation: 'bounce-down',
  },
])

const handleCurrentCharacterClick = () => {
  // 火山引擎上报：Landing页面开始按钮点击
  track('landing_start_click', {
    page: 'landing',
    action: 'start_button_click',
    timestamp: Date.now(),
    user_agent: navigator.userAgent,
    page_url: window.location.href,
  })

  // 根据环境配置故事
  const config = useRuntimeConfig()
  const appName = config.public.appName || 'ReelPlay'
  const isReelPlay =
    appName.toLowerCase().includes('reel') || appName === 'ReelPlay'
  const isProduction = process.env.NODE_ENV === 'production'

  let storyId: string
  let characterKey: string

  if (isReelPlay && isProduction) {
    // ReelPlay 正式版环境：50% 50% 随机选择两个故事
    const stories = [
      {
        storyId: '77c906a3-b3c7-4293-b48a-309f44cec442',
        characterKey: 'b470bd7e-bbca-422b-99bc-daebdf24d98a',
      },
      {
        storyId: '5ffc96ac-41ac-4dde-b96d-e01d93a0e5c6',
        characterKey: 'b470bd7e-bbca-422b-99bc-daebdf24d98a',
      },
    ]

    // 随机选择一个故事
    const randomIndex = Math.floor(Math.random() * stories.length)
    const selectedStory = stories[randomIndex]
    storyId = selectedStory.storyId
    characterKey = selectedStory.characterKey

    // 火山引擎上报：故事选择
    track('landing_story_selected', {
      story_id: storyId,
      character_key: characterKey,
      selection_method: 'random',
      environment: 'production',
      app_name: appName,
    })
  } else {
    // 其他环境（PlayShot 或开发环境）使用原来的固定配置
    storyId = 'c4336930-1e64-40d0-a9f4-e9a65a798951'
    characterKey = '248abfa2-861c-463d-89b4-5de7665e6abe'

    // 火山引擎上报：故事选择
    track('landing_story_selected', {
      story_id: storyId,
      character_key: characterKey,
      selection_method: 'fixed',
      environment: isProduction ? 'production' : 'development',
      app_name: appName,
    })
  }

  // 火山引擎上报：页面跳转
  track('landing_navigation', {
    from_page: 'landing',
    to_page: 'chat4',
    target_url: `/chat4/${storyId}/${characterKey}`,
    story_id: storyId,
    character_key: characterKey,
  })

  // 直接跳转到chat4页面
  navigateTo(`/chat4/${storyId}/${characterKey}`)
}

// 初始化
onMounted(async () => {
  try {
    isLoading.value = true

    // 火山引擎上报：Landing页面访问
    trackPageView('/landing')

    // 火山引擎上报：页面加载完成
    track('landing_page_loaded', {
      page: 'landing',
      load_time: Date.now(),
      user_agent: navigator.userAgent,
      referrer: document.referrer || 'direct',
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
    })

    await new Promise((resolve) => setTimeout(resolve, 500))

    // 自动轮播功能图片 (已注释)
    // setInterval(nextFeature, 5000)
  } catch (error) {
    console.error('Failed to initialize Chat4 cosmic page:', error)

    // 火山引擎上报：页面加载错误
    track('landing_page_error', {
      page: 'landing',
      error_message: (error as Error)?.message || 'Unknown error',
      error_stack: (error as Error)?.stack || '',
      timestamp: Date.now(),
    })
  } finally {
    isLoading.value = false
  }
})

// SEO 设置
usePageSeo({
  pageTitle: 'Chat4 Cosmic - Discover Your Perfect Connection',
  customDescription:
    'Meet charming companions and create meaningful conversations in a beautiful, cosmic world. Chat with handsome characters designed for female users. Free to start!',
})
</script>

<style lang="less" scoped>
@import '~/assets/css/chat4.less';

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: #09090a;
  color: #fafafa;
  overflow-x: hidden;
}

.chat4-cosmic-page {
  min-height: calc(var(--vh, 1vh) * 100);
  background: #09090a;
  color: #fafafa;
  overflow-x: hidden;
  position: relative;
}

/* Cosmic Background */
.cosmic-bg {
  background: radial-gradient(
    ellipse at center,
    rgba(9, 9, 10, 0.95) 0%,
    rgba(9, 9, 10, 1) 100%
  );
  position: relative;
}

.cosmic-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(
      circle at 20% 30%,
      rgba(255, 255, 255, 0.1) 1px,
      transparent 1px
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(255, 255, 255, 0.08) 1px,
      transparent 1px
    ),
    radial-gradient(
      circle at 40% 70%,
      rgba(255, 255, 255, 0.12) 1px,
      transparent 1px
    ),
    radial-gradient(
      circle at 90% 80%,
      rgba(255, 255, 255, 0.06) 1px,
      transparent 1px
    ),
    radial-gradient(
      circle at 10% 90%,
      rgba(255, 255, 255, 0.1) 1px,
      transparent 1px
    );
  background-size:
    400px 400px,
    600px 600px,
    300px 300px,
    800px 800px,
    500px 500px;
  animation: twinkle 8s ease-in-out infinite alternate;
  pointer-events: none;
}

/* Animations */
@keyframes twinkle {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

@keyframes bounceUp {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes bounceDown {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(20px);
  }
}

@keyframes float1 {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-15px) rotate(1deg);
  }
  66% {
    transform: translateY(5px) rotate(-1deg);
  }
}

@keyframes float2 {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(10px) rotate(-1deg);
  }
  75% {
    transform: translateY(-5px) rotate(1deg);
  }
}

.bounce-up {
  animation: bounceUp 6s ease-in-out infinite;
}

.bounce-down {
  animation: bounceDown 6s ease-in-out infinite;
}

.float-1 {
  animation: float1 8s ease-in-out infinite;
}

.float-2 {
  animation: float2 10s ease-in-out infinite;
}

/* Effects */
.glow-effect {
  filter: drop-shadow(0 0 20px rgba(181, 156, 135, 0.3))
    drop-shadow(0 0 40px rgba(181, 156, 135, 0.2))
    drop-shadow(0 0 80px rgba(181, 156, 135, 0.1));
}

.character-glow {
  filter: drop-shadow(0 0 30px rgba(255, 255, 255, 0.2))
    drop-shadow(0 0 60px rgba(181, 156, 135, 0.3))
    drop-shadow(0 0 100px rgba(181, 156, 135, 0.2));
}

/* Header */
.header {
  position: relative;
  z-index: 20;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  width: 48px;
  height: 48px;
  background: rgba(181, 156, 135, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.logo-text {
  font-size: 24px;
  font-weight: bold;
  color: #b59c87;
}

/* Hero Section */
.hero-section {
  position: relative;
  min-height: calc(var(--vh, 1vh) * 100);
  overflow: hidden;
}

.floating-card {
  width: 162px;
  height: 288px;
  border-radius: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  opacity: 0.85;
  transition: all 0.3s ease;
  object-fit: cover;
  pointer-events: none; /* 不可点击，纯装饰 */
  backdrop-filter: blur(4px);
}

/* 左侧卡片：往右倾斜 + 透明度渐变 */
.floating-card.left-side {
  mask: linear-gradient(to left, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 1));
  -webkit-mask: linear-gradient(to left, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 1));
  transform-style: preserve-3d;
  perspective: 1000px;
}

/* 右侧卡片：往左倾斜 + 透明度渐变 */
.floating-card.right-side {
  mask: linear-gradient(to right, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 1));
  -webkit-mask: linear-gradient(to right, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 1));
  transform-style: preserve-3d;
  perspective: 1000px;
}

/* 分散在页面各个位置的装饰卡片 - 3D效果 */
.card-1 {
  position: absolute;
  left: 10%;
  top: 8%;
  // transform: rotate(-12deg) rotateY(250deg) rotateX(5deg);
}

.card-2 {
  position: absolute;
  left: 8%;
  bottom: 20%;
  transform: rotate(8deg) rotateY(20deg) rotateX(-3deg);
}

.card-3 {
  position: absolute;
  right: 2%;
  top: 12%;
  transform: rotate(-15deg) rotateY(-25deg) rotateX(4deg);
}

.card-4 {
  position: absolute;
  right: 6%;
  bottom: 25%;
  transform: rotate(10deg) rotateY(-20deg) rotateX(-2deg);
}

.card-5 {
  position: absolute;
  left: 25%;
  top: 35%;
  transform: rotate(-8deg) rotateY(15deg) rotateX(3deg);
}

.card-6 {
  position: absolute;
  right: 25%;
  top: 45%;
  transform: rotate(12deg) rotateY(-18deg) rotateX(-4deg);
}

/* Main Content */
.main-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 20;
  padding-bottom: 80px;
  text-align: center;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.main-title {
  font-size: 4rem;
  font-weight: bold;
  margin-bottom: 24px;
  line-height: 1.1;
}

.gradient-text {
  background: linear-gradient(to right, #b59c87, #fde047, #b59c87);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-2 {
  background: linear-gradient(to right, #60a5fa, #a78bfa, #f472b6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.description {
  font-size: 1.5rem;
  color: #d1d5db;
  margin-bottom: 48px;
  max-width: 896px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.play-buttons {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  margin-top: 32px;
}

@media (min-width: 640px) {
  .play-buttons {
    flex-direction: row;
    justify-content: center;
  }
}

.play-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(181, 156, 135, 0.1);
  border: 1px solid rgba(181, 156, 135, 0.3);
  padding: 12px 24px;
  border-radius: 9999px;
  color: #b59c87;
  text-decoration: none;
  transition: all 0.3s;
  backdrop-filter: blur(8px);
  cursor: pointer;
}

.play-btn:hover {
  background: rgba(181, 156, 135, 0.2);
  transform: translateY(-2px);
}

/* 主角图 */
.hero-character {
  position: absolute;
  bottom: 0;
  z-index: -10;
  left: 50%;
  transform: translate(-50%);
}

.hero-character .character-image {
  max-width: 400px;
  width: 100%;
  height: auto;
  transition: transform 0.3s ease;
}

.hero-character .character-image:hover {
  transform: scale(1.02);
}

/* SEO Content */
.seo-content {
  position: absolute;
  left: -9999px;
  top: -9999px;
  visibility: hidden;
}

.seo-hidden {
  position: absolute;
  left: -9999px;
  top: -9999px;
  visibility: hidden;
}

/* Responsive */
@media (max-width: 768px) {
  .main-title {
    font-size: 2.5rem;
  }

  .description {
    font-size: 1.25rem;
    margin-bottom: 32px;
  }

  .play-buttons {
    margin-top: 24px;
    margin-bottom: 48px;
  }

  .play-btn {
    padding: 10px 20px;
    font-size: 0.9rem;
  }
  .hero-character {
    top: -20%;
  }
  /* 移动端主角图 */
  .hero-character .character-image {
    max-width: 300px;
  }

  /* 移动端装饰卡片样式 */
  .floating-card {
    width: 126px;
    height: 224px;
    border: 1.5px solid rgba(255, 255, 255, 0.25);
    opacity: 0.85; /* 移动端稍微透明一些 */
  }

  .card-1 {
    left: 5%;
    top: 15%;
    transform: rotate(-10deg) rotateY(20deg) rotateX(3deg);
  }

  .card-2 {
    left: 5%;
    bottom: 5%;
    transform: rotate(6deg) rotateY(15deg) rotateX(-2deg);
  }

  .card-3 {
    right: 5%;
    top: 5%;
    transform: rotate(-12deg) rotateY(-20deg) rotateX(3deg);
  }

  .card-4 {
    right: 4%;
    bottom: 10%;
    transform: rotate(8deg) rotateY(-15deg) rotateX(-2deg);
  }

  .card-5 {
    display: none;
    left: 50%;
    bottom: 3%;
    transform: rotate(-6deg) rotateY(12deg) rotateX(2deg);
  }

  .card-6 {
    display: none;
    right: 30%;
    top: 60%;
    transform: rotate(10deg) rotateY(-15deg) rotateX(-3deg);
  }
}
</style>
