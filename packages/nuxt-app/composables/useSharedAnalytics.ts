/**
 * Nuxt应用统一事件上报配置
 * 集成新的shared analytics架构
 * 主要使用火山引擎统计
 */

import { 
  AnalyticsManager, 
  ByteDanceProvider,  // 主要使用火山引擎
  GoogleAnalyticsProvider,
  type EventData,
  ReportEvent,
  EventHelpers
} from '@magic-partner/shared/analytics'

let analytics: AnalyticsManager | null = null

/**
 * 初始化分析服务
 */
function initAnalytics() {
  if (analytics) return analytics

  analytics = new AnalyticsManager({
    debug: process.env.NODE_ENV === 'development'
  })

  // 主要使用字节跳动火山引擎
  analytics.addProvider(new ByteDanceProvider())
  
  // 谷歌分析作为备用（可选启用）
  const runtimeConfig = useRuntimeConfig()
  if (runtimeConfig.public.enableGA) {
    const { brandingConfig } = useBranding()
    const appName = brandingConfig.value.appName.toLowerCase()
    const gaId = appName.includes('reel') ? 'G-7J3CFG8D6T' : 'G-BCD38QPPKH'
    analytics.addProvider(new GoogleAnalyticsProvider(gaId))
  }

  return analytics
}

/**
 * Nuxt Composable - 统一事件上报
 */
export const useSharedAnalytics = () => {
  // 确保只在客户端初始化
  const analyticsInstance = process.client ? initAnalytics() : null

  /**
   * 统一事件上报
   */
  const track = (eventName: string, parameters?: EventData) => {
    if (!analyticsInstance || !process.client) return

    try {
      // 添加品牌信息
      const { brandingConfig } = useBranding()
      const enrichedData = {
        ...parameters,
        brand: brandingConfig.value.appName,
        timestamp: Date.now()
      }

      analyticsInstance.track(eventName, enrichedData)
    } catch (error) {
      console.error('[Nuxt Analytics] 事件上报失败:', error)
    }
  }

  /**
   * 页面浏览事件
   */
  const trackPageView = (pagePath?: string) => {
    if (!analyticsInstance || !process.client) return

    try {
      const path = pagePath || window.location.pathname
      analyticsInstance.pageview(path)
    } catch (error) {
      console.error('[Nuxt Analytics] 页面浏览上报失败:', error)
    }
  }

  /**
   * 故事相关事件 - 保持与原有useAnalytics兼容
   */
  const trackStoryEvent = (action: string, storyId: string, storyTitle?: string) => {
    track('story_interaction', {
      action,
      story_id: storyId,
      story_title: storyTitle
    })
  }

  /**
   * 用户行为事件
   */
  const trackUserEvent = (action: string, details?: EventData) => {
    track('user_action', {
      action,
      ...details
    })
  }

  /**
   * 支付相关事件
   */
  const trackPaymentEvent = (action: string, amount?: number, currency?: string) => {
    track('payment', {
      action,
      value: amount,
      currency: currency || 'USD'
    })
  }

  /**
   * 使用事件助手函数
   */
  const trackWithHelper = (helperName: keyof typeof EventHelpers, ...args: any[]) => {
    if (!analyticsInstance || !process.client) return

    try {
      const helper = EventHelpers[helperName] as Function
      if (helper) {
        const { event, data } = helper(...args)
        analyticsInstance.track(event, data)
      }
    } catch (error) {
      console.error(`[Nuxt Analytics] ${helperName} 事件上报失败:`, error)
    }
  }

  /**
   * 获取GA ID - 保持与原有useAnalytics兼容
   */
  const getGAId = () => {
    const { brandingConfig } = useBranding()
    const appName = brandingConfig.value.appName.toLowerCase()
    return appName.includes('reel') ? 'G-7J3CFG8D6T' : 'G-BCD38QPPKH'
  }

  return {
    // 新的统一接口
    track,
    trackPageView,
    trackWithHelper,
    
    // 保持向后兼容的接口
    trackStoryEvent,
    trackUserEvent, 
    trackPaymentEvent,
    getGAId,
    
    // 原始分析实例
    analytics: analyticsInstance
  }
}