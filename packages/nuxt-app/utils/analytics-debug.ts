/**
 * 开发环境统计分析调试工具
 * 用于验证火山引擎和谷歌分析上报是否成功
 */

/**
 * 在浏览器控制台中验证统计分析配置
 */
export function debugAnalytics() {
  console.group('🔍 Analytics Debug Info')
  
  // 检查火山引擎
  console.group('🌋 火山引擎 (ByteDance)')
  console.log('window.collectEvent 存在:', !!window.collectEvent)
  if (window.collectEvent) {
    console.log('✅ 火山引擎已加载')
    // 测试上报
    try {
      window.collectEvent('debug_test', {
        test: true,
        timestamp: Date.now()
      })
      console.log('✅ 测试事件上报成功')
    } catch (error) {
      console.error('❌ 测试事件上报失败:', error)
    }
  } else {
    console.warn('❌ 火山引擎未加载')
  }
  console.groupEnd()
  
  // 检查谷歌分析
  console.group('📊 谷歌分析 (Google Analytics)')
  console.log('window.gtag 存在:', !!window.gtag)
  console.log('window.dataLayer 存在:', !!window.dataLayer)
  if (window.gtag) {
    console.log('✅ 谷歌分析已加载')
    // 测试上报
    try {
      window.gtag('event', 'debug_test', {
        test: true,
        timestamp: Date.now()
      })
      console.log('✅ 测试事件上报成功')
    } catch (error) {
      console.error('❌ 测试事件上报失败:', error)
    }
  } else {
    console.warn('❌ 谷歌分析未加载')
  }
  console.groupEnd()
  
  // 检查网络请求
  console.group('🌐 网络请求监控')
  console.log('打开 Network 面板，筛选以下域名:')
  console.log('- 火山引擎: gator.volces.com, toblog.ctobsnssdk.com')
  console.log('- 谷歌分析: google-analytics.com, googletagmanager.com')
  console.groupEnd()
  
  console.groupEnd()
}

/**
 * 监听统计分析网络请求
 */
export function monitorAnalyticsRequests() {
  const originalFetch = window.fetch
  const originalXHROpen = XMLHttpRequest.prototype.open
  
  // 监听 fetch 请求
  window.fetch = function(...args) {
    const url = args[0]?.toString() || ''
    if (isAnalyticsRequest(url)) {
      console.log('📡 Analytics Request (fetch):', url)
    }
    return originalFetch.apply(this, args)
  }
  
  // 监听 XMLHttpRequest
  XMLHttpRequest.prototype.open = function(method, url, ...rest) {
    if (isAnalyticsRequest(url.toString())) {
      console.log('📡 Analytics Request (XHR):', url)
    }
    return originalXHROpen.apply(this, [method, url, ...rest])
  }
  
  console.log('🔍 Analytics 网络请求监控已启用')
}

/**
 * 判断是否为统计分析相关请求
 */
function isAnalyticsRequest(url: string): boolean {
  const analyticsHosts = [
    'gator.volces.com',
    'toblog.ctobsnssdk.com',
    'google-analytics.com',
    'googletagmanager.com',
    'analytics.google.com'
  ]
  
  return analyticsHosts.some(host => url.includes(host))
}

/**
 * 在开发环境自动启用调试
 */
if (process.env.NODE_ENV === 'development') {
  // 延迟执行，确保页面加载完成
  setTimeout(() => {
    console.log('🔧 开发环境 Analytics 调试工具已加载')
    console.log('使用 debugAnalytics() 查看配置信息')
    console.log('使用 monitorAnalyticsRequests() 监控网络请求')
    
    // 自动添加到全局对象
    ;(window as any).debugAnalytics = debugAnalytics
    ;(window as any).monitorAnalyticsRequests = monitorAnalyticsRequests
  }, 1000)
}

// 类型声明
declare global {
  interface Window {
    collectEvent?: (eventType: string, eventData?: any) => void
    gtag?: (...args: any[]) => void
    dataLayer?: any[]
    debugAnalytics?: () => void
    monitorAnalyticsRequests?: () => void
  }
}
